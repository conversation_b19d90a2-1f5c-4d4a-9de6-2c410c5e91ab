services:
  remotetask:
    image: lightglm:v1.0
    volumes:
      - /usr/local/dcmi:/usr/local/dcmi
      - /usr/local/bin/npu-smi:/usr/local/bin/npu-smi
      - /usr/local/sbin/npu-smi:/usr/local/sbin/npu-smi
      - /usr/local/Ascend/driver:/usr/local/Ascend/driver
      - /usr/local/Ascend/firmware:/usr/local/Ascend/firmware
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime:ro
      - /data/applications/spss/LightGBM:/workspace
    ports:
      - 44919:8080
    environment:
      - PYTHONPATH=/workspace
      - INPUT_DATA={\"data_path\":\"/data/applications/spss/LightGBM/data/titanic_train.json\"}
      - DEBUG_MODE=1
      - COMPONENT_PARAMS={\"task_type\":\"binary_classification\",\"label_column\":\"ddd\",\"feature_columns\":\"aaa\",\"categorical_columns\":\"ff\",\"num_boost_round\":\"500\",\"early_stopping_rounds\":\"30\",\"learning_rate\":\"0.1\",\"verbose_eval\":\"25\"}
      - SESSION_ID=debug-1788327887749120-eaf5575c
      - COMPONENT_NAME=LightGBM二分类
    command: "python train.py --data_path /workspace/data/titanic_train.json --task_type binary_classification --label_column ddd --feature_columns aaa --categorical_columns ff --num_boost_round 500 --early_stopping_rounds 30 --learning_rate 0.1 --verbose_eval 25 --debug"