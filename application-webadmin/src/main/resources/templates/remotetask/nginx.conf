user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    keepalive_timeout  65;

    # 关键WebSocket优化配置
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;
	proxy_set_header Origin "";
    
    # 禁用代理缓冲，避免WebSocket消息延迟
    proxy_buffering off;
    
    # 延长超时时间
    proxy_read_timeout 86400;
    proxy_connect_timeout 600;
	proxy_socket_keepalive on;
	tcp_nodelay on;

    gzip  on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Code Server 反向代理配置
    server {
        listen       80;
        server_name  _;

        # 精确匹配根路径
        location = / {
            return 404;
        }

        #  code Servicer
        location ~ ^/code1935332558959022080/ {
            proxy_pass http://codeserver1-host:9000;
            rewrite ^/code1935332558959022080/(.*)$ /$1 break;
            access_log /var/log/nginx/codeserver_access.log main;
            error_log /var/log/nginx/codeserver_error.log debug;
        }

        # jupyter 代理配置
         location ~ ^/code1935332558959022080/(.*)$ {
                set $target http://codeserver1-host:9000; # 目标服务器地址

                proxy_pass $target/$1;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header Origin "";

                # WebSocket 支持
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";

                # 禁用缓冲以优化实时性
                proxy_buffering off;

                # 延长超时时间
                proxy_read_timeout 86400s;
                proxy_connect_timeout 600s;
                proxy_send_timeout 86400s;

            }

            # 普通nginx 代理配置
            location / {
                    proxy_pass http://:8080;  # 后端服务地址
                    proxy_set_header Host $host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;

                    proxy_buffering off;        # 禁用缓冲，适合实时应用
                    proxy_cache off;            # 禁用缓存

                    proxy_read_timeout 300s;
                    proxy_send_timeout 300s;
                    proxy_connect_timeout 60s;
                }

    }
}
